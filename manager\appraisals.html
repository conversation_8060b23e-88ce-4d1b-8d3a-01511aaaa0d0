<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appraisals - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Filter section styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link active">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisals</h1>
                <div>
                    <button id="exportAppraisalsBtn" class="btn btn-success">
                        <i class="fas fa-download mr-1"></i> Export to Excel
                    </button>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-2">
                            <label for="teamScopeFilter" class="form-label small">Team Scope</label>
                            <select id="teamScopeFilter" class="form-select form-select-sm">
                                <option value="direct">My Direct Team</option>
                                <option value="extended">My Extended Team</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="directTeamFilter" class="form-label small">My Direct Team Filter</label>
                            <select id="directTeamFilter" class="form-select form-select-sm">
                                <option value="">All Direct Reports</option>
                                <!-- Direct team members will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="extendedTeamManagersFilter" class="form-label small">My Extended Team Filter</label>
                            <select id="extendedTeamManagersFilter" class="form-select form-select-sm" disabled>
                                <option value="">Select a parent manager first</option>
                                <!-- Extended team managers will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="yearFilter" class="form-label small">Year</label>
                            <select id="yearFilter" class="form-select form-select-sm">
                                <option value="">All Years</option>
                                <!-- Years will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="periodTypeFilter" class="form-label small">Period Type</label>
                            <select id="periodTypeFilter" class="form-select form-select-sm">
                                <option value="">All Periods</option>
                                <option value="Q1">Q1</option>
                                <option value="Q2">Q2</option>
                                <option value="Q3">Q3</option>
                                <option value="Q4">Q4</option>
                                <option value="Semester 1">Semester 1</option>
                                <option value="Semester 2">Semester 2</option>
                                <option value="Annual">Annual</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Appraisals Table -->
            <div class="card">
                <div class="card-header py-2">
                    <h5 class="mb-0">Appraisal History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="appraisalsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Position</th>
                                    <th>Direct Manager</th>
                                    <th>Period</th>
                                    <th>Total Score</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="appraisalsTableBody">
                                <!-- Appraisals will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }

            // User name will be loaded with manager data
            
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load filters
            loadFilters();
            
            // Load appraisals
            loadAppraisals();
            
            // Auto-apply filters on dropdown change
            document.getElementById('teamScopeFilter').addEventListener('change', function() {
                loadAppraisals();
            });

            document.getElementById('directTeamFilter').addEventListener('change', function() {
                applyDirectTeamFilter();
            });

            document.getElementById('extendedTeamManagersFilter').addEventListener('change', function() {
                applyExtendedTeamManagersFilter();
            });

            document.getElementById('yearFilter').addEventListener('change', function() {
                loadAppraisals();
            });

            document.getElementById('periodTypeFilter').addEventListener('change', function() {
                loadAppraisals();
            });
            
            // Export to Excel button
            document.getElementById('exportAppraisalsBtn').addEventListener('click', function() {
                appUtils.exportToExcel('appraisalsTable', 'Appraisals_History');
            });
        });

        // Get all team members in the hierarchy (recursive)
        async function getAllTeamMembers(managerCode) {
            try {
                const allMembers = [];

                // Get direct reports
                const { data: directReports, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('manager_code', managerCode);

                if (error) throw error;

                if (directReports && directReports.length > 0) {
                    allMembers.push(...directReports);

                    // Recursively get reports of reports
                    for (const employee of directReports) {
                        const subTeam = await getAllTeamMembers(employee.code_number);
                        allMembers.push(...subTeam);
                    }
                }

                return allMembers;
            } catch (error) {
                console.error('Error getting team members:', error);
                return [];
            }
        }

        // Load filter options
        async function loadFilters() {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) return;

                // Update user name display
                document.getElementById('currentUserName').textContent = currentManager.name;

                // Load direct team filter
                await loadDirectTeamFilter(currentManager.code_number);
                
                // Load years from periods
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name')
                    .order('start_date', { ascending: false });

                if (periods) {
                    // Extract unique years from period names
                    const years = [...new Set(periods.map(period => {
                        const match = period.name.match(/\d{4}/);
                        return match ? parseInt(match[0]) : null;
                    }).filter(year => year !== null))].sort((a, b) => b - a);

                    const yearFilter = document.getElementById('yearFilter');
                    const currentYear = new Date().getFullYear();

                    years.forEach(year => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        yearFilter.appendChild(option);
                    });

                    // Auto-select current year if available
                    if (years.includes(currentYear)) {
                        yearFilter.value = currentYear;
                    }
                }
                
            } catch (error) {
                console.error('Error loading filters:', error);
                appUtils.showNotification('Error loading filters', 'error');
            }
        }

        // Load direct team filter
        async function loadDirectTeamFilter(managerCode) {
            try {
                // Get direct reports only
                const { data: directReports } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('manager_code', managerCode)
                    .order('name');

                const directTeamFilter = document.getElementById('directTeamFilter');
                directTeamFilter.innerHTML = '<option value="">All Direct Reports</option>';

                if (directReports) {
                    directReports.forEach(employee => {
                        const option = document.createElement('option');
                        option.value = employee.code_number;
                        option.textContent = employee.name;
                        directTeamFilter.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('Error loading direct team filter:', error);
            }
        }

        // Apply direct team filter
        async function applyDirectTeamFilter() {
            const selectedEmployeeCode = document.getElementById('directTeamFilter').value;

            if (!selectedEmployeeCode) {
                // Show all direct reports
                disableExtendedTeamManagersFilter();
                loadAppraisals();
                return;
            }

            // Check if selected employee is a manager and load their extended team managers
            const { data: selectedEmployee } = await supabaseClient
                .from('employees')
                .select('code_number, name, is_manager')
                .eq('code_number', selectedEmployeeCode)
                .single();

            if (selectedEmployee && selectedEmployee.is_manager) {
                // Populate the extended team managers filter with this manager's direct manager reports
                await populateExtendedTeamManagersFilter(selectedEmployeeCode);
            } else {
                // Disable extended team filter if selected employee is not a manager
                disableExtendedTeamManagersFilter();
            }

            loadAppraisals();
        }

        // Populate extended team managers filter based on selected parent manager
        async function populateExtendedTeamManagersFilter(parentManagerCode) {
            try {
                const extendedTeamManagersFilter = document.getElementById('extendedTeamManagersFilter');
                extendedTeamManagersFilter.innerHTML = '<option value="">All Extended Team</option>';

                // Get direct reports of the selected parent manager who are also managers
                const { data: managerReports } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('manager_code', parentManagerCode)
                    .eq('is_manager', true)
                    .order('name');

                if (managerReports && managerReports.length > 0) {
                    managerReports.forEach(manager => {
                        const option = document.createElement('option');
                        option.value = manager.code_number;
                        option.textContent = manager.name;
                        extendedTeamManagersFilter.appendChild(option);
                    });

                    // Enable the filter
                    extendedTeamManagersFilter.disabled = false;
                } else {
                    // No manager reports, disable the filter
                    extendedTeamManagersFilter.disabled = true;
                }
            } catch (error) {
                console.error('Error loading extended team managers:', error);
                disableExtendedTeamManagersFilter();
            }
        }

        // Disable extended team managers filter
        function disableExtendedTeamManagersFilter() {
            const extendedTeamManagersFilter = document.getElementById('extendedTeamManagersFilter');
            extendedTeamManagersFilter.innerHTML = '<option value="">Select a parent manager first</option>';
            extendedTeamManagersFilter.disabled = true;
        }

        // Apply extended team managers filter
        async function applyExtendedTeamManagersFilter() {
            loadAppraisals();
        }

        // Load appraisals
        async function loadAppraisals() {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) return;
                
                // Get filter values
                const teamScope = document.getElementById('teamScopeFilter').value;
                const directTeamFilter = document.getElementById('directTeamFilter').value;
                const extendedTeamManagersFilter = document.getElementById('extendedTeamManagersFilter').value;
                const yearFilter = document.getElementById('yearFilter').value;
                const periodTypeFilter = document.getElementById('periodTypeFilter').value;

                // Get team members based on filters
                let teamMemberCodes = [];

                if (extendedTeamManagersFilter) {
                    // Show selected manager and their complete extended team
                    const extendedTeamMembers = await getAllTeamMembers(extendedTeamManagersFilter);
                    teamMemberCodes = [extendedTeamManagersFilter, ...extendedTeamMembers.map(emp => emp.code_number)];
                } else if (directTeamFilter) {
                    // Check if selected employee is a manager
                    const { data: selectedEmployee } = await supabaseClient
                        .from('employees')
                        .select('code_number, is_manager')
                        .eq('code_number', directTeamFilter)
                        .single();

                    if (selectedEmployee && selectedEmployee.is_manager) {
                        // Show selected manager and their complete extended team
                        const extendedTeamMembers = await getAllTeamMembers(directTeamFilter);
                        teamMemberCodes = [directTeamFilter, ...extendedTeamMembers.map(emp => emp.code_number)];
                    } else {
                        // Show only selected employee (non-manager)
                        teamMemberCodes = [directTeamFilter];
                    }
                } else {
                    // Show team based on scope
                    if (teamScope === 'extended') {
                        const extendedTeamMembers = await getAllTeamMembers(currentManager.code_number);
                        teamMemberCodes = extendedTeamMembers.map(emp => emp.code_number);
                    } else {
                        // Show all direct reports
                        const { data: directReports } = await supabaseClient
                            .from('employees')
                            .select('code_number')
                            .eq('manager_code', currentManager.code_number);
                        teamMemberCodes = (directReports || []).map(emp => emp.code_number);
                    }
                }

                // Build query
                let query = supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(code_number, name, position, manager_code),
                        manager:employees!appraisals_manager_code_fkey(code_number, name),
                        period:appraisal_periods(id, name)
                    `)
                    .in('employee_code', teamMemberCodes);

                // Add period filtering based on year and period type
                if (yearFilter || periodTypeFilter) {
                    // Get all periods first to filter by name pattern
                    const { data: allPeriods } = await supabaseClient
                        .from('appraisal_periods')
                        .select('id, name');

                    if (allPeriods) {
                        let filteredPeriodIds = [];

                        allPeriods.forEach(period => {
                            let matches = true;

                            // Check year filter
                            if (yearFilter) {
                                const yearMatch = period.name.match(/\d{4}/);
                                if (!yearMatch || yearMatch[0] !== yearFilter) {
                                    matches = false;
                                }
                            }

                            // Check period type filter
                            if (periodTypeFilter && matches) {
                                if (!period.name.startsWith(periodTypeFilter + ' ')) {
                                    matches = false;
                                }
                            }

                            if (matches) {
                                filteredPeriodIds.push(period.id);
                            }
                        });

                        if (filteredPeriodIds.length > 0) {
                            query = query.in('period_id', filteredPeriodIds);
                        } else {
                            // No matching periods found, return empty result
                            query = query.eq('period_id', 'no-match');
                        }
                    }
                }
                
                // Execute query
                const { data: appraisals, error } = await query.order('created_at', { ascending: false });
                
                if (error) throw error;
                
                // Populate table
                await populateAppraisalsTable(appraisals || []);
                
            } catch (error) {
                console.error('Error loading appraisals:', error);
                appUtils.showNotification('Error loading appraisals', 'error');
            }
        }
        
        // Helper function to get manager name
        async function getManagerName(managerCode) {
            if (!managerCode) return 'N/A';

            try {
                const { data: manager, error } = await supabaseClient
                    .from('employees')
                    .select('name')
                    .eq('code_number', managerCode)
                    .single();

                if (error) throw error;
                return manager ? manager.name : 'N/A';
            } catch (error) {
                console.error('Error fetching manager name:', error);
                return 'N/A';
            }
        }

        // Populate appraisals table
        async function populateAppraisalsTable(appraisals) {
            const tableBody = document.getElementById('appraisalsTableBody');
            tableBody.innerHTML = '';
            
            if (appraisals.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 8;
                cell.textContent = 'No appraisals found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            // Process appraisals with async operations
            for (const appraisal of appraisals) {
                const row = document.createElement('tr');

                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = appraisal.employee ? appraisal.employee.name : 'Unknown';
                row.appendChild(employeeCell);

                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = appraisal.employee ? (appraisal.employee.position || 'N/A') : 'N/A';
                row.appendChild(positionCell);

                // Direct Manager
                const managerCell = document.createElement('td');
                if (appraisal.employee && appraisal.employee.manager_code) {
                    const managerName = await getManagerName(appraisal.employee.manager_code);
                    managerCell.textContent = managerName;
                } else {
                    managerCell.textContent = 'N/A';
                }
                row.appendChild(managerCell);

                // Period
                const periodCell = document.createElement('td');
                periodCell.textContent = appraisal.period ? appraisal.period.name : 'Unknown';
                row.appendChild(periodCell);

                // Total Score (with % symbol)
                const totalScoreCell = document.createElement('td');
                totalScoreCell.innerHTML = appraisal.total_score ? `<strong>${appraisal.total_score.toFixed(1)}%</strong>` : 'N/A';
                row.appendChild(totalScoreCell);
                
                // Grade
                const gradeCell = document.createElement('td');
                if (appraisal.grade) {
                    gradeCell.textContent = appraisal.grade;
                    gradeCell.style.color = appUtils.getGradeColor(appraisal.grade);
                } else {
                    gradeCell.textContent = 'N/A';
                }
                row.appendChild(gradeCell);
                
                // Status - NEW WORKFLOW
                const statusCell = document.createElement('td');
                let status = 'Draft';
                let statusClass = 'badge-warning';

                if (appraisal.manager_signature && appraisal.employee_signature) {
                    status = 'Completed';
                    statusClass = 'badge-success';
                } else if (appraisal.manager_signature && appraisal.employee_signature_requested) {
                    status = 'Pending Employee';
                    statusClass = 'badge-info';
                } else if (appraisal.manager_signature) {
                    status = 'Submitted';
                    statusClass = 'badge-primary';
                }

                statusCell.innerHTML = `<span class="badge ${statusClass}">${status}</span>`;
                row.appendChild(statusCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <a href="view-appraisal.html?id=${appraisal.id}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> View
                    </a>
                    ${!appraisal.manager_signature ? `
                    <a href="create-appraisal.html?id=${appraisal.id}" class="btn btn-primary btn-sm ml-1">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    ` : ''}
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });

    </script>
</body>
</html>