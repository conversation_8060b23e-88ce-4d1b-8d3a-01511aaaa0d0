<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Appraisal - HR Performance Evaluation System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <style>
        /* My Appraisal specific styling */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 5px 0;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        <nav class="nav-bottom">
            <a href="team.html" class="nav-link">
                <i class="fas fa-users"></i> My Team
            </a>
            <a href="appraisals.html" class="nav-link">
                <i class="fas fa-clipboard-check"></i> Appraisals
            </a>
            <a href="reports.html" class="nav-link">
                <i class="fas fa-chart-bar"></i> Reports
            </a>
            <a href="my-appraisal.html" class="nav-link active">
                <i class="fas fa-user-check"></i> My Appraisal
            </a>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="page-title mb-0">My Latest Performance Appraisal</h1>
                <a href="#" id="myKpisBtn" class="btn btn-outline-primary" title="View My KPIs">
                    <i class="fas fa-tasks"></i> My KPIs
                </a>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading your appraisal data...</p>
            </div>

            <!-- Latest Appraisal Summary -->
            <div id="latestAppraisalSection" style="display: none;">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="totalScore">-</div>
                            <div class="stat-label">Total Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="performanceScore">-</div>
                            <div class="stat-label">Performance Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="behavioralScore">-</div>
                            <div class="stat-label">Behavioral Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="currentGrade">-</div>
                            <div class="stat-label">Current Grade</div>
                        </div>
                    </div>
                </div>

                <!-- Latest Appraisal Charts -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Total Score</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="totalScoreGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="performanceGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Behavioral</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="behavioralGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historical Appraisals Table -->
            <div id="historySection" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history"></i> Appraisal History
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Period</th>
                                        <th>Total Score</th>
                                        <th>Performance</th>
                                        <th>Behavioral</th>
                                        <th>Grade</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Data State -->
            <div id="noDataState" class="no-data" style="display: none;">
                <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                <h3>No Appraisals Found</h3>
                <p>You don't have any performance appraisals yet.</p>
                <p class="text-muted">This is normal if you are a senior manager without a direct supervisor, or if no appraisals have been assigned to you yet.</p>
            </div>
        </div>
    </main>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/supabase.js"></script>

    <script>
        let totalScoreChart = null;
        let performanceChart = null;
        let behavioralChart = null;
        let allAppraisals = [];

        document.addEventListener('DOMContentLoaded', async function() {
            // Check authentication
            if (!appAuth.isAuthenticated()) {
                window.location.href = '../index.html';
                return;
            }

            // User name will be loaded with manager data

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Logout functionality
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // My KPIs button functionality
            document.getElementById('myKpisBtn').addEventListener('click', function(e) {
                e.preventDefault();
                const currentEmployee = appAuth.getCurrentEmployee();
                if (currentEmployee) {
                    window.location.href = `view-employee-kpis.html?employee=${currentEmployee.code_number}`;
                } else {
                    appUtils.showNotification('Employee information not found', 'error');
                }
            });

            // Load my appraisals
            loadMyAppraisals();
        });



        async function loadMyAppraisals() {
            try {
                console.log('Loading my appraisals...');
                const currentManager = appAuth.getCurrentEmployee();

                if (!currentManager) {
                    throw new Error('No current manager found');
                }

                // Update user name
                document.getElementById('currentUserName').textContent = currentManager.name;

                // Load appraisals for current manager
                const { data: appraisals, error } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        period:appraisal_periods(id, name, start_date, end_date)
                    `)
                    .eq('employee_code', currentManager.code_number)
                    .order('created_at', { ascending: false });

                console.log('My appraisals query result:', appraisals, error);

                if (error) throw error;

                allAppraisals = appraisals || [];

                // Hide loading state
                document.getElementById('loadingState').style.display = 'none';

                if (allAppraisals.length === 0) {
                    // Show no data state
                    document.getElementById('noDataState').style.display = 'block';
                } else {
                    // Show latest appraisal summary
                    displayLatestAppraisal(allAppraisals[0]);

                    // Show history table
                    displayAppraisalHistory(allAppraisals);

                    document.getElementById('latestAppraisalSection').style.display = 'block';
                    document.getElementById('historySection').style.display = 'block';
                }

            } catch (error) {
                console.error('Error loading my appraisals:', error);
                document.getElementById('loadingState').innerHTML = `
                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    <p class="text-danger">Error loading appraisal data: ${error.message}</p>
                `;
            }
        }

        function displayLatestAppraisal(appraisal) {
            // Update stats
            document.getElementById('totalScore').textContent = `${appraisal.total_score}%`;
            document.getElementById('performanceScore').textContent = `${appraisal.performance_score}%`;
            document.getElementById('behavioralScore').textContent = `${appraisal.behavioral_score}%`;
            document.getElementById('currentGrade').textContent = appraisal.grade;

            // Create charts
            createLatestAppraisalCharts(appraisal);
        }

        function createLatestAppraisalCharts(appraisal) {
            // Destroy existing charts
            if (totalScoreChart) totalScoreChart.destroy();
            if (performanceChart) performanceChart.destroy();
            if (behavioralChart) behavioralChart.destroy();

            // Create Total Score Gauge
            totalScoreChart = createGaugeChart('totalScoreGauge', parseFloat(appraisal.total_score), 'Total Score');

            // Create Performance Gauge
            performanceChart = createGaugeChart('performanceGauge', parseFloat(appraisal.performance_score), 'Performance');

            // Create Behavioral Gauge
            behavioralChart = createGaugeChart('behavioralGauge', parseFloat(appraisal.behavioral_score), 'Behavioral');
        }

        function displayAppraisalHistory(appraisals) {
            const tbody = document.getElementById('historyTableBody');
            tbody.innerHTML = '';

            appraisals.forEach(appraisal => {
                const row = document.createElement('tr');

                // Format date
                const date = new Date(appraisal.created_at).toLocaleDateString();

                // Get grade class
                const gradeClass = getGradeClass(appraisal.grade);

                row.innerHTML = `
                    <td>${appraisal.period?.name || 'N/A'}</td>
                    <td><strong>${appraisal.total_score}%</strong></td>
                    <td>${appraisal.performance_score}%</td>
                    <td>${appraisal.behavioral_score}%</td>
                    <td><span class="grade-badge ${gradeClass}">${appraisal.grade}</span></td>
                    <td>${date}</td>
                    <td>
                        <a href="view-appraisal.html?id=${appraisal.id}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        function getGradeClass(grade) {
            const gradeMap = {
                'Excellent': 'grade-excellent',
                'Very Good': 'grade-very-good',
                'Meets Requirements': 'grade-meets-requirements',
                'Needs Improvement': 'grade-needs-improvement',
                'Poor': 'grade-poor'
            };
            return gradeMap[grade] || 'grade-meets-requirements';
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>
