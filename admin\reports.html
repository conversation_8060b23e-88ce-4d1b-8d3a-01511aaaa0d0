<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Report specific styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .report-card {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
            margin-bottom: 20px;
        }

        .report-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 5px 0;
        }

        /* Compact card styling */
        .report-card {
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
            border-radius: 8px;
        }

        .report-card .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
        }

        .report-card .card-body {
            background: #f8f9fa;
        }

        /* Compact filter section */
        .filter-section {
            padding: 10px 15px;
            margin-bottom: 15px;
        }

        .filter-section .form-select,
        .filter-section .btn {
            padding: 5px 10px;
            font-size: 0.9rem;
        }

        /* Sortable table styles */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sort-icon {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sortable.asc .sort-icon:before {
            content: "\f0de";
            opacity: 1;
        }

        .sortable.desc .sort-icon:before {
            content: "\f0dd";
            opacity: 1;
        }

        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .report-card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
            color: #e0e0e0;
        }

        /* New header layout */
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 0.75rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        /* Logo styles handled by global CSS */

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-name {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .logout-btn {
            color: white;
            text-decoration: none;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .nav-bottom {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .nav-bottom .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Table text color fixes for dark mode */
        body.dark-mode .table td {
            color: #e0e0e0;
        }

        body.dark-mode .table tbody tr {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link active">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Performance Reports</h1>
                <div>
                    <button id="exportReportBtn" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i> Export Report
                    </button>
                    <button id="printReportBtn" class="btn btn-secondary ms-2">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="totalAppraisals">0</div>
                        <div class="stat-label">Total Appraisals</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averagePerformance">0%</div>
                        <div class="stat-label">Average Performance %</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageBehaviors">0%</div>
                        <div class="stat-label">Average Behaviors %</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageTotal">0%</div>
                        <div class="stat-label">Average Total Evaluation %</div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-1">
                            <label for="yearFilter" class="form-label small">Year</label>
                            <select id="yearFilter" class="form-select form-select-sm">
                                <option value="">All Years</option>
                                <!-- Years will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="periodTypeFilter" class="form-label small">Period Type</label>
                            <select id="periodTypeFilter" class="form-select form-select-sm">
                                <option value="">All Periods</option>
                                <option value="Q1">Q1</option>
                                <option value="Q2">Q2</option>
                                <option value="Q3">Q3</option>
                                <option value="Q4">Q4</option>
                                <option value="Semester 1">Semester 1</option>
                                <option value="Semester 2">Semester 2</option>
                                <option value="Annual">Annual</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="departmentFilter" class="form-label small">Department</label>
                            <select id="departmentFilter" class="form-select form-select-sm">
                                <option value="">All Departments</option>
                                <!-- Departments will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="managerFilter" class="form-label small">Manager</label>
                            <select id="managerFilter" class="form-select form-select-sm">
                                <option value="">All Managers</option>
                                <!-- Managers will be loaded dynamically -->
                            </select>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Charts -->
            <!-- Row 1: Department Performance Charts -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance % vs Behavior %</h5>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="performanceBehaviorChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Total Evaluation %</h5>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="totalDeptChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 2: Grade Distribution and Performance Over Period -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance Grade</h5>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="gradeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance Over Period</h5>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="periodChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 3: Top and Bottom Performers -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Top Performers</h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-success btn-sm" id="topPerformersBtn" onclick="showTopPerformers()">
                                    <i class="fas fa-arrow-up"></i> Top 10
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" id="nextPerformersBtn" onclick="showNextPerformers()" style="display: none;">
                                    <i class="fas fa-arrow-down"></i> Next 10
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="topPerformersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Lowest Performers</h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-danger btn-sm" id="bottomPerformersBtn" onclick="showBottomPerformers()">
                                    <i class="fas fa-arrow-down"></i> Bottom 10
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" id="nextBottomPerformersBtn" onclick="showNextBottomPerformers()" style="display: none;">
                                    <i class="fas fa-arrow-up"></i> Next 10
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <div class="chart-container-compact">
                                <canvas id="bottomPerformersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Report Table -->
            <div class="card">
                <div class="card-header">
                    <h2>Performance Summary</h2>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="reportTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Manager</th>
                                    <th>Period</th>
                                    <th>Performance Score</th>
                                    <th>Behavioral Score</th>
                                    <th class="sortable" onclick="sortTable(6)">
                                        Total Score <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th>Grade</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportTableBody">
                                <!-- Report data will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load filters
            loadFilters();
            
            // Load report data
            loadReportData();
            
            // Auto-apply filters on dropdown change
            document.getElementById('departmentFilter').addEventListener('change', function() {
                loadReportData();
            });

            document.getElementById('yearFilter').addEventListener('change', function() {
                loadReportData();
            });

            document.getElementById('periodTypeFilter').addEventListener('change', function() {
                loadReportData();
            });

            document.getElementById('managerFilter').addEventListener('change', function() {
                loadReportData();
            });
            
            // Export to Excel button
            document.getElementById('exportReportBtn').addEventListener('click', function() {
                appUtils.exportToExcel('reportTable', 'Performance_Report');
            });
        });
        
        // Load filter options
        async function loadFilters() {
            try {
                // Load departments
                const { data: departments } = await supabaseClient
                    .from('employees')
                    .select('department')
                    .order('department');
                
                if (departments) {
                    const uniqueDepartments = [...new Set(departments.map(item => item.department))];
                    const departmentFilter = document.getElementById('departmentFilter');
                    
                    uniqueDepartments.forEach(department => {
                        const option = document.createElement('option');
                        option.value = department;
                        option.textContent = department;
                        departmentFilter.appendChild(option);
                    });
                }
                
                // Load years from appraisal periods
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name')
                    .order('start_date', { ascending: false });

                if (periods) {
                    // Extract unique years from period names
                    const years = [...new Set(periods.map(period => {
                        const match = period.name.match(/\d{4}/);
                        return match ? parseInt(match[0]) : null;
                    }).filter(year => year !== null))].sort((a, b) => b - a);

                    const yearFilter = document.getElementById('yearFilter');
                    const currentYear = new Date().getFullYear();

                    years.forEach(year => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        yearFilter.appendChild(option);
                    });

                    // Auto-select current year if available
                    if (years.includes(currentYear)) {
                        yearFilter.value = currentYear;
                    }
                }
                
                // Load managers
                const { data: managers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('is_manager', true)
                    .order('name');
                
                if (managers) {
                    const managerFilter = document.getElementById('managerFilter');
                    
                    managers.forEach(manager => {
                        const option = document.createElement('option');
                        option.value = manager.code_number;
                        option.textContent = manager.name;
                        managerFilter.appendChild(option);
                    });
                }
                
            } catch (error) {
                console.error('Error loading filters:', error);
                appUtils.showNotification('Error loading filters', 'error');
            }
        }
        
        // Load report data
        async function loadReportData() {
            try {
                console.log('Loading report data...');

                // Get filter values
                const departmentFilter = document.getElementById('departmentFilter').value;
                const yearFilter = document.getElementById('yearFilter').value;
                const periodTypeFilter = document.getElementById('periodTypeFilter').value;
                const managerFilter = document.getElementById('managerFilter').value;

                console.log('Filters:', { departmentFilter, yearFilter, periodTypeFilter, managerFilter });
                
                // Build query for appraisals with employee and manager details
                let query = supabaseClient
                    .from('appraisals')
                    .select(`
                        id,
                        total_score,
                        performance_score,
                        behavioral_score,
                        grade,
                        employee_code,
                        manager_code,
                        period_id,
                        manager_signature,
                        employee_signature,
                        employee_signature_requested,
                        created_at,
                        employee:employees!appraisals_employee_code_fkey(code_number, name, department),
                        manager:employees!appraisals_manager_code_fkey(code_number, name),
                        period:appraisal_periods(id, name)
                    `);
                
                // Apply filters - Note: Department filtering needs to be done client-side due to nested relationship
                // We'll filter after getting the data

                // Add period filtering based on year and period type
                if (yearFilter || periodTypeFilter) {
                    // Get all periods first to filter by name pattern
                    const { data: allPeriods } = await supabaseClient
                        .from('appraisal_periods')
                        .select('id, name');

                    if (allPeriods) {
                        let filteredPeriodIds = [];

                        allPeriods.forEach(period => {
                            let matches = true;

                            // Check year filter
                            if (yearFilter) {
                                const yearMatch = period.name.match(/\d{4}/);
                                if (!yearMatch || yearMatch[0] !== yearFilter) {
                                    matches = false;
                                }
                            }

                            // Check period type filter
                            if (periodTypeFilter && matches) {
                                if (!period.name.startsWith(periodTypeFilter + ' ')) {
                                    matches = false;
                                }
                            }

                            if (matches) {
                                filteredPeriodIds.push(period.id);
                            }
                        });

                        if (filteredPeriodIds.length > 0) {
                            query = query.in('period_id', filteredPeriodIds);
                        } else {
                            // No matching periods found, return empty result
                            query = query.eq('period_id', 'no-match');
                        }
                    }
                }
                
                if (managerFilter) {
                    query = query.eq('manager_code', managerFilter);
                }
                
                // Execute query
                console.log('Executing query...');
                const { data: appraisals, error } = await query;

                if (error) {
                    console.error('Query error:', error);
                    throw error;
                }

                console.log('Query result:', appraisals);

                // Apply client-side department filter if needed
                let filteredAppraisals = appraisals || [];
                if (departmentFilter) {
                    filteredAppraisals = filteredAppraisals.filter(appraisal =>
                        appraisal.employee && appraisal.employee.department === departmentFilter
                    );
                }

                console.log('Filtered appraisals:', filteredAppraisals);

                // Calculate and update analytics
                updateAnalytics(filteredAppraisals);

                // Populate report table
                populateReportTable(filteredAppraisals);

                // Generate charts
                generateCharts(filteredAppraisals);
                
            } catch (error) {
                console.error('Error loading report data:', error);
                appUtils.showNotification('Error loading report data', 'error');
            }
        }

        // Update analytics section
        function updateAnalytics(appraisals) {
            // Show total appraisals count
            document.getElementById('totalAppraisals').textContent = appraisals.length;

            if (appraisals && appraisals.length > 0) {
                // Calculate average performance percentage
                const totalPerformance = appraisals.reduce((sum, appraisal) => sum + (appraisal.performance_score || 0), 0);
                const averagePerformance = totalPerformance / appraisals.length;
                document.getElementById('averagePerformance').textContent = `${averagePerformance.toFixed(1)}%`;

                // Calculate average behavioral percentage
                const totalBehavioral = appraisals.reduce((sum, appraisal) => sum + (appraisal.behavioral_score || 0), 0);
                const averageBehavioral = totalBehavioral / appraisals.length;
                document.getElementById('averageBehaviors').textContent = `${averageBehavioral.toFixed(1)}%`;

                // Calculate average total evaluation percentage
                const totalEvaluation = appraisals.reduce((sum, appraisal) => sum + (appraisal.total_score || 0), 0);
                const averageTotal = totalEvaluation / appraisals.length;
                document.getElementById('averageTotal').textContent = `${averageTotal.toFixed(1)}%`;
            } else {
                document.getElementById('averagePerformance').textContent = 'N/A';
                document.getElementById('averageBehaviors').textContent = 'N/A';
                document.getElementById('averageTotal').textContent = 'N/A';
            }
        }

        // Populate report table
        function populateReportTable(appraisals) {
            console.log('Populating table with appraisals:', appraisals);
            const tableBody = document.getElementById('reportTableBody');
            tableBody.innerHTML = '';

            if (appraisals.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 9;
                cell.textContent = 'No appraisal data found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            appraisals.forEach(appraisal => {
                const row = document.createElement('tr');
                
                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = appraisal.employee ? appraisal.employee.name : 'Unknown';
                row.appendChild(employeeCell);
                
                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = appraisal.employee ? appraisal.employee.department : 'Unknown';
                row.appendChild(departmentCell);
                
                // Manager
                const managerCell = document.createElement('td');
                managerCell.textContent = appraisal.manager ? appraisal.manager.name : 'Unknown';
                row.appendChild(managerCell);
                
                // Period
                const periodCell = document.createElement('td');
                periodCell.textContent = appraisal.period ? appraisal.period.name : 'Unknown';
                row.appendChild(periodCell);
                
                // Performance Score
                const performanceScoreCell = document.createElement('td');
                performanceScoreCell.textContent = appraisal.performance_score ? `${appraisal.performance_score.toFixed(2)}%` : 'N/A';
                row.appendChild(performanceScoreCell);
                
                // Behavioral Score
                const behavioralScoreCell = document.createElement('td');
                behavioralScoreCell.textContent = appraisal.behavioral_score ? `${appraisal.behavioral_score.toFixed(2)}%` : 'N/A';
                row.appendChild(behavioralScoreCell);
                
                // Total Score
                const totalScoreCell = document.createElement('td');
                totalScoreCell.textContent = appraisal.total_score ? `${appraisal.total_score.toFixed(2)}%` : 'N/A';
                row.appendChild(totalScoreCell);
                
                // Grade
                const gradeCell = document.createElement('td');
                const gradeColor = appUtils.getGradeColor(appraisal.grade);
                gradeCell.innerHTML = `<span class="badge" style="background-color: ${gradeColor}">${appraisal.grade || 'N/A'}</span>`;
                row.appendChild(gradeCell);

                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <a href="view-appraisal.html?id=${appraisal.id}" class="btn btn-info btn-sm me-1" title="View Appraisal">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteAppraisal('${appraisal.id}', '${appraisal.employee.name}', '${appraisal.period.name}')" title="Delete Appraisal">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            });
        }
        
        // Generate charts
        function generateCharts(appraisals) {
            // Combined performance and behavior chart
            generatePerformanceBehaviorChart(appraisals);

            // Total evaluation by department
            generateTotalDeptChart(appraisals);

            // Grade distribution chart
            generateGradeChart(appraisals);

            // Performance over period chart
            generatePeriodChart(appraisals);

            // Top and bottom performers charts
            generateTopPerformersChart(appraisals);
            generateBottomPerformersChart(appraisals);
        }
        
        // Generate combined performance and behavior chart
        function generatePerformanceBehaviorChart(appraisals) {
            const departmentData = {};

            appraisals.forEach(appraisal => {
                if (appraisal.employee && appraisal.employee.department) {
                    const department = appraisal.employee.department;
                    if (!departmentData[department]) {
                        departmentData[department] = {
                            performance: { total: 0, count: 0 },
                            behavior: { total: 0, count: 0 }
                        };
                    }

                    if (appraisal.performance_score) {
                        departmentData[department].performance.total += parseFloat(appraisal.performance_score);
                        departmentData[department].performance.count++;
                    }

                    if (appraisal.behavioral_score) {
                        departmentData[department].behavior.total += parseFloat(appraisal.behavioral_score);
                        departmentData[department].behavior.count++;
                    }
                }
            });

            const departments = [];
            const performanceScores = [];
            const behaviorScores = [];

            for (const department in departmentData) {
                departments.push(department);

                const perfAvg = departmentData[department].performance.count > 0
                    ? departmentData[department].performance.total / departmentData[department].performance.count
                    : 0;
                performanceScores.push(perfAvg.toFixed(1));

                const behavAvg = departmentData[department].behavior.count > 0
                    ? departmentData[department].behavior.total / departmentData[department].behavior.count
                    : 0;
                behaviorScores.push(behavAvg.toFixed(1));
            }

            if (window.performanceBehaviorChart && typeof window.performanceBehaviorChart.destroy === 'function') {
                window.performanceBehaviorChart.destroy();
            }

            // Create double bar chart
            const ctx = document.getElementById('performanceBehaviorChart').getContext('2d');
            window.performanceBehaviorChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: departments,
                    datasets: [
                        {
                            label: 'Performance %',
                            data: performanceScores,
                            backgroundColor: '#4a90e2',
                            borderColor: '#4a90e2',
                            borderWidth: 1,
                            borderRadius: 8,
                            borderSkipped: false
                        },
                        {
                            label: 'Behavior %',
                            data: behaviorScores,
                            backgroundColor: '#27ae60',
                            borderColor: '#27ae60',
                            borderWidth: 1,
                            borderRadius: 8,
                            borderSkipped: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: { usePointStyle: true, padding: 15 }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            grid: { display: false }
                        }
                    }
                }
            });
        }

        // Generate total evaluation by department chart
        function generateTotalDeptChart(appraisals) {
            const departmentData = {};

            appraisals.forEach(appraisal => {
                if (appraisal.employee && appraisal.employee.department && appraisal.total_score) {
                    const department = appraisal.employee.department;
                    if (!departmentData[department]) {
                        departmentData[department] = { totalScore: 0, count: 0 };
                    }
                    departmentData[department].totalScore += parseFloat(appraisal.total_score);
                    departmentData[department].count++;
                }
            });

            const departments = [];
            const averageScores = [];

            for (const department in departmentData) {
                departments.push(department);
                const average = departmentData[department].totalScore / departmentData[department].count;
                averageScores.push(average.toFixed(1));
            }

            if (window.totalDeptChart && typeof window.totalDeptChart.destroy === 'function') {
                window.totalDeptChart.destroy();
            }

            // Create modern bar chart
            const ctx = document.getElementById('totalDeptChart').getContext('2d');
            window.totalDeptChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: departments,
                    datasets: [{
                        label: 'Total Evaluation %',
                        data: averageScores,
                        backgroundColor: '#3498db',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            grid: { display: false }
                        }
                    }
                }
            });
        }

        // Generate performance over period chart
        function generatePeriodChart(appraisals) {
            const periodData = {};

            appraisals.forEach(appraisal => {
                if (appraisal.period && appraisal.period.name && appraisal.total_score) {
                    const period = appraisal.period.name;
                    if (!periodData[period]) {
                        periodData[period] = { totalScore: 0, count: 0 };
                    }
                    periodData[period].totalScore += parseFloat(appraisal.total_score);
                    periodData[period].count++;
                }
            });

            // Sort periods in chronological order (Q1, Q2, Semester 1, Q3, Q4, Semester 2, Annual)
            const periodOrder = ['Q1', 'Q2', 'Semester 1', 'Q3', 'Q4', 'Semester 2', 'Annual'];
            const periods = Object.keys(periodData).sort((a, b) => {
                const indexA = periodOrder.findIndex(p => a.includes(p));
                const indexB = periodOrder.findIndex(p => b.includes(p));
                if (indexA === -1 && indexB === -1) return a.localeCompare(b);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            });

            const averageScores = [];

            periods.forEach(period => {
                const average = periodData[period].totalScore / periodData[period].count;
                averageScores.push(average.toFixed(1));
            });

            if (window.periodChart && typeof window.periodChart.destroy === 'function') {
                window.periodChart.destroy();
            }

            // Create line chart
            const ctx = document.getElementById('periodChart').getContext('2d');
            window.periodChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: periods,
                    datasets: [{
                        label: 'Performance %',
                        data: averageScores,
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#f39c12',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            grid: { display: false }
                        }
                    }
                }
            });
        }
        
        // Generate grade distribution chart
        function generateGradeChart(appraisals) {
            // Count appraisals by grade
            const gradeData = {
                'Poor': 0,
                'Need Improvement': 0,
                'Meet Requirements': 0,
                'Very Good': 0,
                'Excellent': 0
            };

            appraisals.forEach(appraisal => {
                let grade = appraisal.grade;
                // Consolidate legacy grade names
                if (grade === 'Meets Requirements') {
                    grade = 'Meet Requirements';
                }
                if (grade && gradeData.hasOwnProperty(grade)) {
                    gradeData[grade]++;
                }
            });
            
            // Prepare data for chart
            const grades = Object.keys(gradeData);
            const counts = Object.values(gradeData);
            
            // Create chart
            const ctx = document.getElementById('gradeChart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (window.gradeChart && typeof window.gradeChart.destroy === 'function') {
                window.gradeChart.destroy();
            }
            
            // Create modern pie chart with proper sizing and centering
            window.gradeChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: grades,
                    datasets: [{
                        data: counts,
                        backgroundColor: [
                            '#e74c3c',  // Poor - Red
                            '#f39c12',  // Need Improvement - Orange
                            '#27ae60',  // Meets Requirements - Green
                            '#3498db',  // Very Good - Blue
                            '#f1c40f'   // Excellent - Yellow
                        ],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 12 }
                            }
                        }
                    },
                    layout: {
                        padding: {
                            left: 10,
                            right: 10,
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }
        
        // Generate performance trend chart
        function generateTrendChart(appraisals) {
            // Group appraisals by period and calculate average scores
            const periodData = {};
            
            appraisals.forEach(appraisal => {
                if (appraisal.period && appraisal.period.name) {
                    const periodName = appraisal.period.name;
                    
                    if (!periodData[periodName]) {
                        periodData[periodName] = {
                            performance: {
                                total: 0,
                                count: 0
                            },
                            behavioral: {
                                total: 0,
                                count: 0
                            },
                            overall: {
                                total: 0,
                                count: 0
                            }
                        };
                    }
                    
                    if (appraisal.performance_score) {
                        periodData[periodName].performance.total += appraisal.performance_score;
                        periodData[periodName].performance.count++;
                    }
                    
                    if (appraisal.behavioral_score) {
                        periodData[periodName].behavioral.total += appraisal.behavioral_score;
                        periodData[periodName].behavioral.count++;
                    }
                    
                    if (appraisal.total_score) {
                        periodData[periodName].overall.total += appraisal.total_score;
                        periodData[periodName].overall.count++;
                    }
                }
            });
            
            // Calculate averages
            const periods = [];
            const performanceScores = [];
            const behavioralScores = [];
            const overallScores = [];
            
            // Sort periods chronologically (assuming period names are sortable)
            const sortedPeriods = Object.keys(periodData).sort();
            
            sortedPeriods.forEach(period => {
                periods.push(period);
                
                const performanceAvg = periodData[period].performance.count > 0 
                    ? periodData[period].performance.total / periodData[period].performance.count 
                    : 0;
                performanceScores.push(performanceAvg);
                
                const behavioralAvg = periodData[period].behavioral.count > 0 
                    ? periodData[period].behavioral.total / periodData[period].behavioral.count 
                    : 0;
                behavioralScores.push(behavioralAvg);
                
                const overallAvg = periodData[period].overall.count > 0 
                    ? periodData[period].overall.total / periodData[period].overall.count 
                    : 0;
                overallScores.push(overallAvg);
            });
            
            // Create datasets
            const datasets = [
                {
                    label: 'Overall',
                    data: overallScores,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true,
                    tension: 0.1
                },
                {
                    label: 'Performance',
                    data: performanceScores,
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    fill: true,
                    tension: 0.1
                },
                {
                    label: 'Behavioral',
                    data: behavioralScores,
                    borderColor: '#fd7e14',
                    backgroundColor: 'rgba(253, 126, 20, 0.1)',
                    fill: true,
                    tension: 0.1
                }
            ];
            
            // Create chart
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (window.trendChart && typeof window.trendChart.destroy === 'function') {
                window.trendChart.destroy();
            }
            
            window.trendChart = appCharts.createLineChart('trendChart', periods, datasets, 'Performance Trend by Period');
        }

        // Helper function to get average scores per employee per year
        function getAverageScoresPerEmployee(appraisals) {
            const employeeMap = {};

            // Group appraisals by employee (only for the filtered year/period)
            appraisals.forEach(appraisal => {
                const empCode = appraisal.employee_code;
                if (!employeeMap[empCode]) {
                    employeeMap[empCode] = {
                        employee: appraisal.employee,
                        employee_code: empCode,
                        totalScores: [],
                        performanceScores: [],
                        behavioralScores: [],
                        periods: []
                    };
                }

                if (appraisal.total_score !== null && appraisal.total_score !== undefined) {
                    employeeMap[empCode].totalScores.push(appraisal.total_score);
                }
                if (appraisal.performance_score !== null && appraisal.performance_score !== undefined) {
                    employeeMap[empCode].performanceScores.push(appraisal.performance_score);
                }
                if (appraisal.behavioral_score !== null && appraisal.behavioral_score !== undefined) {
                    employeeMap[empCode].behavioralScores.push(appraisal.behavioral_score);
                }
                if (appraisal.period?.name) {
                    employeeMap[empCode].periods.push(appraisal.period.name);
                }
            });

            // Calculate averages for each employee (within the selected year/period filters)
            return Object.values(employeeMap).map(emp => ({
                employee: emp.employee,
                employee_code: emp.employee_code,
                avgTotalScore: emp.totalScores.length > 0 ?
                    emp.totalScores.reduce((sum, score) => sum + score, 0) / emp.totalScores.length : 0,
                avgPerformanceScore: emp.performanceScores.length > 0 ?
                    emp.performanceScores.reduce((sum, score) => sum + score, 0) / emp.performanceScores.length : 0,
                avgBehavioralScore: emp.behavioralScores.length > 0 ?
                    emp.behavioralScores.reduce((sum, score) => sum + score, 0) / emp.behavioralScores.length : 0,
                appraisalCount: emp.totalScores.length,
                periods: [...new Set(emp.periods)] // Unique periods for this employee
            }));
        }

        // Global variables to track current performer views
        let currentPerformerOffset = 0;
        let currentBottomPerformerOffset = 0;

        // Generate top performers chart - Based on employee averages per year
        function generateTopPerformersChart(appraisals) {
            // Calculate average scores per employee for the selected year/period
            const employeeAverages = getAverageScoresPerEmployee(appraisals);

            // Sort by average total score (descending)
            const sortedEmployees = employeeAverages
                .filter(emp => emp.avgTotalScore > 0 && emp.employee && emp.employee.name)
                .sort((a, b) => b.avgTotalScore - a.avgTotalScore);

            // Get top 10 performers
            const topPerformers = sortedEmployees.slice(0, 10);

            const names = topPerformers.map(emp => emp.employee.name);
            const scores = topPerformers.map(emp => emp.avgTotalScore.toFixed(1));

            if (window.topPerformersChart && typeof window.topPerformersChart.destroy === 'function') {
                window.topPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('topPerformersChart').getContext('2d');
            window.topPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#27ae60',
                        borderColor: '#27ae60',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Store sorted data for navigation
            window.sortedPerformers = sortedEmployees;
            currentPerformerOffset = 0;

            // Show/hide navigation buttons
            const nextBtn = document.getElementById('nextPerformersBtn');
            if (sortedAppraisals.length > 10) {
                nextBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'none';
            }
        }

        // Generate bottom performers chart - Based on employee averages per year
        function generateBottomPerformersChart(appraisals) {
            // Calculate average scores per employee for the selected year/period
            const employeeAverages = getAverageScoresPerEmployee(appraisals);

            // Sort by average total score (ascending)
            const sortedEmployees = employeeAverages
                .filter(emp => emp.avgTotalScore > 0 && emp.employee && emp.employee.name)
                .sort((a, b) => a.avgTotalScore - b.avgTotalScore);

            // Get bottom 10 performers
            const bottomPerformers = sortedEmployees.slice(0, 10);

            const names = bottomPerformers.map(emp => emp.employee.name);
            const scores = bottomPerformers.map(emp => emp.avgTotalScore.toFixed(1));

            if (window.bottomPerformersChart && typeof window.bottomPerformersChart.destroy === 'function') {
                window.bottomPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('bottomPerformersChart').getContext('2d');
            window.bottomPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#e74c3c',
                        borderColor: '#e74c3c',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Store sorted data for navigation (reverse order for bottom performers)
            window.sortedBottomPerformers = sortedEmployees;
            currentBottomPerformerOffset = 0;

            // Show/hide navigation buttons
            const nextBtn = document.getElementById('nextBottomPerformersBtn');
            if (sortedAppraisals.length > 10) {
                nextBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'none';
            }
        }

        // Show top performers (reset to top 10)
        function showTopPerformers() {
            if (!window.sortedPerformers) return;

            currentPerformerOffset = 0;
            const topPerformers = window.sortedPerformers.slice(0, 10);

            const names = topPerformers.map(appraisal => appraisal.employee.name);
            const scores = topPerformers.map(appraisal => parseFloat(appraisal.total_score).toFixed(1));

            if (window.topPerformersChart && typeof window.topPerformersChart.destroy === 'function') {
                window.topPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('topPerformersChart').getContext('2d');
            window.topPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#27ae60',
                        borderColor: '#27ae60',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Update button states
            document.getElementById('topPerformersBtn').classList.add('btn-success');
            document.getElementById('topPerformersBtn').classList.remove('btn-outline-success');
            document.getElementById('nextPerformersBtn').classList.add('btn-outline-warning');
            document.getElementById('nextPerformersBtn').classList.remove('btn-warning');
            document.getElementById('nextPerformersBtn').innerHTML = '<i class="fas fa-arrow-down"></i> Next 10';
        }

        // Show next 10 performers (navigate down the list)
        function showNextPerformers() {
            if (!window.sortedPerformers) return;

            currentPerformerOffset += 10;

            // Check if we've reached the end
            if (currentPerformerOffset >= window.sortedPerformers.length) {
                currentPerformerOffset = window.sortedPerformers.length - 10;
                if (currentPerformerOffset < 0) currentPerformerOffset = 0;
            }

            const nextPerformers = window.sortedPerformers.slice(currentPerformerOffset, currentPerformerOffset + 10);

            const names = nextPerformers.map(emp => emp.employee.name);
            const scores = nextPerformers.map(emp => emp.avgTotalScore.toFixed(1));

            if (window.topPerformersChart && typeof window.topPerformersChart.destroy === 'function') {
                window.topPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('topPerformersChart').getContext('2d');
            window.topPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#f39c12',
                        borderColor: '#f39c12',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Update button states
            document.getElementById('topPerformersBtn').classList.add('btn-outline-success');
            document.getElementById('topPerformersBtn').classList.remove('btn-success');
            document.getElementById('nextPerformersBtn').classList.add('btn-warning');
            document.getElementById('nextPerformersBtn').classList.remove('btn-outline-warning');

            // Update button text based on position
            const remaining = window.sortedPerformers.length - currentPerformerOffset - 10;
            if (remaining > 0) {
                document.getElementById('nextPerformersBtn').innerHTML = `<i class="fas fa-arrow-down"></i> Next 10`;
            } else {
                document.getElementById('nextPerformersBtn').innerHTML = `<i class="fas fa-arrow-up"></i> Back to Top`;
                // If at the end, next click should go back to top
                document.getElementById('nextPerformersBtn').onclick = showTopPerformers;
            }
        }

        // Show bottom performers (reset to bottom 10)
        function showBottomPerformers() {
            if (!window.sortedBottomPerformers) return;

            currentBottomPerformerOffset = 0;
            const bottomPerformers = window.sortedBottomPerformers.slice(0, 10);

            const names = bottomPerformers.map(appraisal => appraisal.employee.name);
            const scores = bottomPerformers.map(appraisal => parseFloat(appraisal.total_score).toFixed(1));

            if (window.bottomPerformersChart && typeof window.bottomPerformersChart.destroy === 'function') {
                window.bottomPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('bottomPerformersChart').getContext('2d');
            window.bottomPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#e74c3c',
                        borderColor: '#e74c3c',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Update button states
            document.getElementById('bottomPerformersBtn').classList.add('btn-danger');
            document.getElementById('bottomPerformersBtn').classList.remove('btn-outline-danger');
            document.getElementById('nextBottomPerformersBtn').classList.add('btn-outline-warning');
            document.getElementById('nextBottomPerformersBtn').classList.remove('btn-warning');
            document.getElementById('nextBottomPerformersBtn').innerHTML = '<i class="fas fa-arrow-up"></i> Next 10';
        }

        // Show next 10 bottom performers (navigate up the list - worse performers)
        function showNextBottomPerformers() {
            if (!window.sortedBottomPerformers) return;

            currentBottomPerformerOffset += 10;

            // Check if we've reached the end
            if (currentBottomPerformerOffset >= window.sortedBottomPerformers.length) {
                currentBottomPerformerOffset = window.sortedBottomPerformers.length - 10;
                if (currentBottomPerformerOffset < 0) currentBottomPerformerOffset = 0;
            }

            const nextBottomPerformers = window.sortedBottomPerformers.slice(currentBottomPerformerOffset, currentBottomPerformerOffset + 10);

            const names = nextBottomPerformers.map(emp => emp.employee.name);
            const scores = nextBottomPerformers.map(emp => emp.avgTotalScore.toFixed(1));

            if (window.bottomPerformersChart && typeof window.bottomPerformersChart.destroy === 'function') {
                window.bottomPerformersChart.destroy();
            }

            // Create modern horizontal bar chart
            const ctx = document.getElementById('bottomPerformersChart').getContext('2d');
            window.bottomPerformersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: names,
                    datasets: [{
                        label: 'Score %',
                        data: scores,
                        backgroundColor: '#f39c12',
                        borderColor: '#f39c12',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            grid: { display: false }
                        }
                    }
                }
            });

            // Update button states
            document.getElementById('bottomPerformersBtn').classList.add('btn-outline-danger');
            document.getElementById('bottomPerformersBtn').classList.remove('btn-danger');
            document.getElementById('nextBottomPerformersBtn').classList.add('btn-warning');
            document.getElementById('nextBottomPerformersBtn').classList.remove('btn-outline-warning');

            // Update button text based on position
            const remaining = window.sortedBottomPerformers.length - currentBottomPerformerOffset - 10;
            if (remaining > 0) {
                document.getElementById('nextBottomPerformersBtn').innerHTML = `<i class="fas fa-arrow-up"></i> Next 10`;
            } else {
                document.getElementById('nextBottomPerformersBtn').innerHTML = `<i class="fas fa-arrow-down"></i> Back to Bottom`;
                // If at the end, next click should go back to bottom
                document.getElementById('nextBottomPerformersBtn').onclick = showBottomPerformers;
            }
        }

        // Table sorting functionality
        let sortDirection = 1; // 1 for ascending, -1 for descending

        function sortTable(columnIndex) {
            const table = document.querySelector('#reportTable tbody');
            const rows = Array.from(table.rows);

            // Toggle sort direction
            sortDirection *= -1;

            // Sort rows based on the column
            rows.sort((a, b) => {
                const aValue = parseFloat(a.cells[columnIndex].textContent) || 0;
                const bValue = parseFloat(b.cells[columnIndex].textContent) || 0;
                return (aValue - bValue) * sortDirection;
            });

            // Clear table and re-append sorted rows
            table.innerHTML = '';
            rows.forEach(row => table.appendChild(row));

            // Update sort icon
            const headers = document.querySelectorAll('.sortable');
            headers.forEach(header => {
                header.classList.remove('asc', 'desc');
            });

            const currentHeader = document.querySelector(`th:nth-child(${columnIndex + 1})`);
            if (sortDirection === 1) {
                currentHeader.classList.add('asc');
            } else {
                currentHeader.classList.add('desc');
            }
        }

        // Delete appraisal function
        async function deleteAppraisal(appraisalId, employeeName, periodName) {
            if (!confirm(`Are you sure you want to delete the appraisal for ${employeeName} (${periodName})?\n\nThis will permanently delete:\n- The appraisal record\n- All KPI scores\n- All comments\n\nThis action cannot be undone and will allow the appraisal to be reassigned.`)) {
                return;
            }

            try {
                console.log('Deleting appraisal:', appraisalId);

                // Delete appraisal (CASCADE will automatically delete appraisal_scores)
                const { error } = await supabaseClient
                    .from('appraisals')
                    .delete()
                    .eq('id', appraisalId);

                if (error) throw error;

                appUtils.showNotification(`Appraisal for ${employeeName} deleted successfully. The employee can now be reassigned for this period.`, 'success');

                // Reload reports data to reflect changes
                loadReportsData();

            } catch (error) {
                console.error('Error deleting appraisal:', error);
                appUtils.showNotification('Error deleting appraisal: ' + error.message, 'error');
            }
        }

        // Load current user name
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser && currentUser.username) {
                    document.getElementById('currentUserName').textContent = currentUser.username;
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin User';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin User';
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>
</body>
</html>