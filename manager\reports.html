<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    
    <style>
        /* Report specific styling */
        .report-card {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        
        .report-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 5px 0;
        }

        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        .report-card .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
        }

        .report-card {
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
            border-radius: 8px;
        }

        .report-card .card-body {
            padding: 10px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .report-card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
            color: #e0e0e0;
        }

        /* New header layout */
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 0.75rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        /* Logo styles handled by global CSS */

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-name {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .logout-btn {
            color: white;
            text-decoration: none;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .nav-bottom {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .nav-bottom .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Table text color fixes for dark mode */
        body.dark-mode .table td {
            color: #e0e0e0;
        }

        body.dark-mode .table tbody tr {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link active">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Performance Reports</h1>
                <div>
                    <button id="exportReportBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Export Report
                    </button>
                    <button id="printReportBtn" class="btn btn-secondary ml-2">
                        <i class="fas fa-print mr-1"></i> Print
                    </button>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="totalAppraisals">0</div>
                        <div class="stat-label">Total Appraisals</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageScore">0%</div>
                        <div class="stat-label">Average Score</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averagePerformance">0%</div>
                        <div class="stat-label">Average Performance</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageBehavior">0%</div>
                        <div class="stat-label">Average Behavior</div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-1">
                            <label for="yearFilter" class="form-label small">Year</label>
                            <select id="yearFilter" class="form-select form-select-sm">
                                <option value="">All Years</option>
                                <!-- Years will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="periodTypeFilter" class="form-label small">Period Type</label>
                            <select id="periodTypeFilter" class="form-select form-select-sm">
                                <option value="">All Periods</option>
                                <option value="Q1">Q1</option>
                                <option value="Q2">Q2</option>
                                <option value="Q3">Q3</option>
                                <option value="Q4">Q4</option>
                                <option value="Semester 1">Semester 1</option>
                                <option value="Semester 2">Semester 2</option>
                                <option value="Annual">Annual</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="departmentFilter" class="form-label small">Department</label>
                            <select id="departmentFilter" class="form-select form-select-sm">
                                <option value="">All Departments</option>
                                <!-- Departments will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="employeeFilter" class="form-label small">Employee</label>
                            <select id="employeeFilter" class="form-select form-select-sm">
                                <option value="">All Employees</option>
                                <!-- Employees will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="managerFilter" class="form-label small">Manager</label>
                            <select id="managerFilter" class="form-select form-select-sm">
                                <option value="">All Managers</option>
                                <!-- Managers will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3 text-end">
                            <button id="toggleChartsBtn" class="btn btn-outline-primary btn-sm" onclick="toggleCharts()" title="Toggle Charts Visibility">
                                <i class="fas fa-chart-bar me-1"></i>
                                <span id="toggleChartsText">Hide Charts</span>
                            </button>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div id="chartsSection">
            <div class="row">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2 d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Top Performers</h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-success btn-sm" id="topPerformersBtn" onclick="showTopPerformers()">
                                    <i class="fas fa-arrow-up"></i> Top 10
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" id="nextPerformersBtn" onclick="showNextPerformers()" style="display: none;">
                                    <i class="fas fa-arrow-down"></i> Next 10
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="topPerformersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Performance vs Behavior</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceBehaviorChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Performance Grade</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceGradeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Performance Over Period</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceOverPeriodChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Charts Row -->
            <div class="row">
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Performance Bell Curve</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceBellCurve"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h5 class="mb-0" style="color: #1976d2;">Top 5 Performance KPIs Scores</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="topPerformanceKPIsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div> <!-- End Charts Section -->

            <!-- Detailed Report Table -->
            <div class="report-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Detailed Performance Report</h3>
                    <button id="exportTableBtn" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download mr-1"></i> Export Table
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="reportTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Manager</th>
                                    <th>Period</th>
                                    <th style="cursor: pointer;" onclick="sortTable(4)" title="Click to sort">
                                        Total Score <i id="sortIcon4" class="fas fa-sort"></i>
                                    </th>
                                    <th>Performance %</th>
                                    <th>Behavioral %</th>
                                    <th>Grade</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportTableBody">
                                <!-- Report data will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/supabase.js"></script>

    <script>
        let currentManager = null;
        let allAppraisals = [];
        let filteredAppraisals = [];

        document.addEventListener('DOMContentLoaded', async function() {
            // Check authentication
            if (!appAuth.isAuthenticated()) {
                window.location.href = '../index.html';
                return;
            }

            // User name will be loaded with manager data

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Get current employee
            currentManager = appAuth.getCurrentEmployee();
            console.log('Current manager:', currentManager);
            if (!currentManager) {
                appUtils.showNotification('Manager information not found', 'error');
                return;
            }

            // Update user name display
            document.getElementById('currentUserName').textContent = currentManager.name;

            // Logout functionality
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // Load initial data
            await loadFilters();
            await loadReportData();

            // Auto-apply filters on dropdown change
            document.getElementById('yearFilter').addEventListener('change', applyFilters);
            document.getElementById('periodTypeFilter').addEventListener('change', applyFilters);
            document.getElementById('departmentFilter').addEventListener('change', applyFilters);
            document.getElementById('employeeFilter').addEventListener('change', applyFilters);
            document.getElementById('managerFilter').addEventListener('change', applyFilters);

            // Navigation button event listeners
            document.getElementById('topPerformersBtn').addEventListener('click', function() {
                console.log('Top performers button clicked');
                showTopPerformers();
            });

            document.getElementById('nextPerformersBtn').addEventListener('click', function() {
                console.log('Next performers button clicked');
                showNextPerformers();
            });

            // Event listeners
            document.getElementById('exportReportBtn').addEventListener('click', exportReport);
            document.getElementById('exportTableBtn').addEventListener('click', exportTable);
            document.getElementById('printReportBtn').addEventListener('click', printReport);
        });

        // Load filter options - Enhanced for extended team
        async function loadFilters() {
            try {
                // Load years from periods
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('*')
                    .order('start_date', { ascending: false });

                if (periods) {
                    // Extract unique years from period names
                    const years = [...new Set(periods.map(period => {
                        const match = period.name.match(/\d{4}/);
                        return match ? parseInt(match[0]) : null;
                    }).filter(year => year !== null))].sort((a, b) => b - a);

                    const yearFilter = document.getElementById('yearFilter');
                    const currentYear = new Date().getFullYear();

                    years.forEach(year => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        yearFilter.appendChild(option);
                    });

                    // Auto-select current year if available
                    if (years.includes(currentYear)) {
                        yearFilter.value = currentYear;
                    }
                }

                // Load complete team hierarchy
                const allTeamMembers = await getCompleteTeamHierarchy(currentManager.code_number);

                // Load departments from all team members
                const uniqueDepartments = [...new Set(allTeamMembers.map(emp => emp.department).filter(dept => dept))];
                const departmentSelect = document.getElementById('departmentFilter');
                uniqueDepartments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept;
                    option.textContent = dept;
                    departmentSelect.appendChild(option);
                });

                // Load employees
                const employeeSelect = document.getElementById('employeeFilter');
                allTeamMembers.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.code_number;
                    option.textContent = employee.name;
                    employeeSelect.appendChild(option);
                });

                // Load managers (from team hierarchy)
                const managerCodes = [...new Set(allTeamMembers.map(emp => emp.manager_code).filter(code => code))];
                const { data: managers } = await supabaseClient
                    .from('employees')
                    .select('name')
                    .in('code_number', managerCodes);

                const uniqueManagers = [...new Set(managers?.map(mgr => mgr.name).filter(name => name))];
                const managerSelect = document.getElementById('managerFilter');
                uniqueManagers.forEach(managerName => {
                    const option = document.createElement('option');
                    option.value = managerName;
                    option.textContent = managerName;
                    managerSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading filters:', error);
                appUtils.showNotification('Error loading filter options', 'error');
            }
        }

        // Recursive function to get complete team hierarchy
        async function getCompleteTeamHierarchy(managerCode, visited = new Set()) {
            // Prevent infinite loops
            if (visited.has(managerCode)) {
                return [];
            }
            visited.add(managerCode);

            try {
                // Get direct reports
                const { data: directReports, error } = await supabaseClient
                    .from('employees')
                    .select('code_number, name, position, department, manager_code')
                    .eq('manager_code', managerCode);

                if (error) throw error;

                let allTeamMembers = directReports || [];

                // Recursively get team members for each direct report
                for (const employee of directReports || []) {
                    const subTeam = await getCompleteTeamHierarchy(employee.code_number, new Set(visited));
                    allTeamMembers = allTeamMembers.concat(subTeam);
                }

                return allTeamMembers;
            } catch (error) {
                console.error('Error getting team hierarchy for manager', managerCode, ':', error);
                return [];
            }
        }

        // Load report data - Enhanced for complete team hierarchy support
        async function loadReportData() {
            try {
                console.log('Manager Reports: Starting loadReportData...');
                console.log('Manager Reports: Current manager:', currentManager);

                // Get direct team members
                const { data: directTeam } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('manager_code', currentManager.code_number);

                console.log('Manager Reports: Found', directTeam?.length || 0, 'direct team members');

                // Get complete team hierarchy (recursive)
                const allTeamMembers = await getCompleteTeamHierarchy(currentManager.code_number);
                console.log('Manager Reports: Found', allTeamMembers.length, 'total team members in hierarchy');

                // Extract team codes
                const allTeamCodes = allTeamMembers.map(emp => emp.code_number);

                if (allTeamCodes.length === 0) {
                    console.log('Manager Reports: No team members found');
                    updateSummaryStats([]);
                    updateCharts([]);
                    updateTable([]);
                    return;
                }

                console.log('Manager Reports: Total team size:', allTeamCodes.length);

                // Load appraisals for all team members
                console.log('Loading appraisals for team members:', allTeamCodes);
                const { data: appraisals, error } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(code_number, name, department, position),
                        manager:employees!appraisals_manager_code_fkey(code_number, name),
                        period:appraisal_periods(id, name, start_date, end_date)
                    `)
                    .in('employee_code', allTeamCodes)
                    .order('created_at', { ascending: false });

                console.log('Manager reports query result:', appraisals, error);

                if (error) throw error;

                allAppraisals = appraisals || [];
                filteredAppraisals = allAppraisals;

                updateSummaryStats(filteredAppraisals);
                await updateCharts(filteredAppraisals);
                updateTable(filteredAppraisals);

            } catch (error) {
                console.error('Error loading report data:', error);
                appUtils.showNotification('Error loading report data', 'error');
            }
        }

        // Apply filters
        async function applyFilters() {
            console.log('Manager reports: Applying filters...');



            const yearFilter = document.getElementById('yearFilter').value;
            const periodTypeFilter = document.getElementById('periodTypeFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;
            const employeeFilter = document.getElementById('employeeFilter').value;
            const managerFilter = document.getElementById('managerFilter').value;
            console.log('Manager reports filters:', { yearFilter, periodTypeFilter, departmentFilter, employeeFilter, managerFilter });

            filteredAppraisals = allAppraisals.filter(appraisal => {
                // Check period filters (year and period type)
                if (yearFilter || periodTypeFilter) {
                    if (!appraisal.period || !appraisal.period.name) return false;

                    let periodMatches = true;

                    // Check year filter
                    if (yearFilter) {
                        const yearMatch = appraisal.period.name.match(/\d{4}/);
                        if (!yearMatch || yearMatch[0] !== yearFilter) {
                            periodMatches = false;
                        }
                    }

                    // Check period type filter
                    if (periodTypeFilter && periodMatches) {
                        if (!appraisal.period.name.startsWith(periodTypeFilter + ' ')) {
                            periodMatches = false;
                        }
                    }

                    if (!periodMatches) return false;
                }
                if (departmentFilter && appraisal.employee?.department !== departmentFilter) return false;
                if (employeeFilter && appraisal.employee_code !== employeeFilter) return false;
                if (managerFilter && appraisal.manager?.name !== managerFilter) return false;
                return true;
            });

            updateSummaryStats(filteredAppraisals);
            await updateCharts(filteredAppraisals);
            updateTable(filteredAppraisals);
        }



        // Update summary statistics
        function updateSummaryStats(appraisals) {
            const totalAppraisals = appraisals.length;
            const averageScore = totalAppraisals > 0 ?
                (appraisals.reduce((sum, a) => sum + (a.total_score || 0), 0) / totalAppraisals).toFixed(1) : 0;
            const averagePerformance = totalAppraisals > 0 ?
                (appraisals.reduce((sum, a) => sum + (a.performance_score || 0), 0) / totalAppraisals).toFixed(1) : 0;
            const averageBehavior = totalAppraisals > 0 ?
                (appraisals.reduce((sum, a) => sum + (a.behavioral_score || 0), 0) / totalAppraisals).toFixed(1) : 0;

            document.getElementById('totalAppraisals').textContent = totalAppraisals;
            document.getElementById('averageScore').textContent = `${averageScore}%`;
            document.getElementById('averagePerformance').textContent = `${averagePerformance}%`;
            document.getElementById('averageBehavior').textContent = `${averageBehavior}%`;
        }

        // Global variables for chart navigation
        let currentPerformerOffset = 0;
        let sortedPerformers = [];

        // Grade color mapping
        const gradeColors = {
            'Poor': '#dc3545',           // Red
            'Need Improvement': '#fd7e14', // Orange
            'Meet Requirements': '#28a745', // Green
            'Very Good': '#007bff',      // Blue
            'Excellent': '#ffc107'       // Yellow
        };

        // Get grade from score
        function getGradeFromScore(score) {
            if (score >= 90) return 'Excellent';
            if (score >= 80) return 'Very Good';
            if (score >= 70) return 'Meet Requirements';
            if (score >= 60) return 'Need Improvement';
            return 'Poor';
        }

        // Show top performers (first 10)
        function showTopPerformers() {
            console.log('showTopPerformers called');
            currentPerformerOffset = 0;
            updateTopPerformersCharts();
            updatePerformerNavigation();
        }

        // Show next 10 performers
        function showNextPerformers() {
            console.log('showNextPerformers called, current offset:', currentPerformerOffset);
            currentPerformerOffset += 10;
            console.log('New offset:', currentPerformerOffset);
            updateTopPerformersCharts();
            updatePerformerNavigation();
        }

        // Update navigation buttons visibility
        function updatePerformerNavigation() {
            const nextBtn = document.getElementById('nextPerformersBtn');
            console.log('Total performers:', sortedPerformers.length, 'Current offset:', currentPerformerOffset);
            if (sortedPerformers.length > currentPerformerOffset + 10) {
                nextBtn.style.display = 'inline-block';
                console.log('Showing next button');
            } else {
                nextBtn.style.display = 'none';
                console.log('Hiding next button');
            }
        }

        // Update charts - New comprehensive chart implementation
        async function updateCharts(appraisals) {
            // Destroy existing charts to prevent memory leaks
            Chart.helpers.each(Chart.instances, function(instance) {
                instance.destroy();
            });

            // Prepare top performers data - Calculate averages like statistics
            console.log('=== DEBUGGING TOP PERFORMERS ===');
            console.log('All appraisals received:', appraisals.length);

            // Calculate average scores per employee (like statistics section)
            const employeeAverages = getAverageScoresPerEmployee(appraisals);
            console.log('Employee averages:', employeeAverages.map(emp => ({
                name: emp.employee?.name,
                avgTotal: emp.avgTotalScore,
                avgPerformance: emp.avgPerformanceScore,
                avgBehavior: emp.avgBehavioralScore,
                count: emp.appraisalCount
            })));

            // Check for excellent average performers
            const excellentAverages = employeeAverages.filter(emp => emp.avgTotalScore >= 90);
            console.log('Employees with average score >= 90:', excellentAverages.map(emp => ({
                name: emp.employee?.name,
                avgScore: emp.avgTotalScore
            })));

            sortedPerformers = employeeAverages
                .filter(emp => emp.avgTotalScore > 0)
                .sort((a, b) => b.avgTotalScore - a.avgTotalScore);

            console.log('Final sorted performers by average:', sortedPerformers.map(p => ({
                name: p.employee?.name,
                avgScore: p.avgTotalScore
            })));
            console.log('=== END DEBUGGING ===');

            currentPerformerOffset = 0;

            // Update all charts
            updateTopPerformersCharts();
            updatePerformerNavigation();
            createPerformanceGradeChart(appraisals);
            createPerformanceOverPeriodChart(appraisals);
            createPerformanceBellCurve(appraisals);
            await createTopKPIsCharts(appraisals);
        }

        // Store chart instances for proper destruction
        let topPerformersChartInstance = null;
        let performanceBehaviorChartInstance = null;

        // Update both top performers charts (synchronized)
        function updateTopPerformersCharts() {
            // Destroy existing chart instances
            if (topPerformersChartInstance) {
                topPerformersChartInstance.destroy();
                topPerformersChartInstance = null;
            }
            if (performanceBehaviorChartInstance) {
                performanceBehaviorChartInstance.destroy();
                performanceBehaviorChartInstance = null;
            }

            const currentPagePerformers = sortedPerformers.slice(currentPerformerOffset, currentPerformerOffset + 10);
            console.log('Current page performers:', currentPagePerformers.map(p => ({
                name: p.employee?.name,
                avgScore: p.avgTotalScore,
                count: p.appraisalCount
            })));

            topPerformersChartInstance = createTopPerformersChart(currentPagePerformers);
            performanceBehaviorChartInstance = createPerformanceBehaviorChart(currentPagePerformers);
        }

        // 1. Top Performers Chart (Left side)
        function createTopPerformersChart(performers) {
            const ctx = document.getElementById('topPerformersChart').getContext('2d');

            const employeeNames = performers.map(emp => emp.employee?.name || 'Unknown');
            const totalScores = performers.map(emp => Math.round(emp.avgTotalScore * 10) / 10); // Round to 1 decimal
            const scoreColors = totalScores.map(score => {
                const grade = getGradeFromScore(score);
                return gradeColors[grade] || '#6c757d';
            });

            return new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: employeeNames,
                    datasets: [{
                        label: 'Total Score %',
                        data: totalScores,
                        backgroundColor: scoreColors,
                        borderColor: scoreColors,
                        borderWidth: 1,
                        borderRadius: 6,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const score = context.parsed.y;
                                    const grade = getGradeFromScore(score);
                                    const employeeData = sortedPerformers[currentPerformerOffset + context.dataIndex];
                                    const count = employeeData ? employeeData.appraisalCount : 1;
                                    return `Average Total Score: ${score}% (${grade}) - ${count} appraisal${count > 1 ? 's' : ''}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 2. Performance vs Behavior Chart (Right side)
        function createPerformanceBehaviorChart(performers) {
            const ctx = document.getElementById('performanceBehaviorChart').getContext('2d');

            const employeeNames = performers.map(emp => emp.employee?.name || 'Unknown');
            const performanceScores = performers.map(emp => Math.round(emp.avgPerformanceScore * 10) / 10); // Round to 1 decimal
            const behavioralScores = performers.map(emp => Math.round(emp.avgBehavioralScore * 10) / 10); // Round to 1 decimal

            // Create color arrays based on individual scores
            const performanceColors = performanceScores.map(score => {
                const grade = getGradeFromScore(score);
                return gradeColors[grade] || '#6c757d';
            });

            const behaviorColors = behavioralScores.map(score => {
                const grade = getGradeFromScore(score);
                return gradeColors[grade] || '#6c757d';
            });

            return new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: employeeNames,
                    datasets: [{
                        label: 'Performance %',
                        data: performanceScores,
                        backgroundColor: performanceColors,
                        borderColor: performanceColors,
                        borderWidth: 1,
                        borderRadius: 6,
                        borderSkipped: false
                    }, {
                        label: 'Behavior %',
                        data: behavioralScores,
                        backgroundColor: behaviorColors,
                        borderColor: behaviorColors,
                        borderWidth: 1,
                        borderRadius: 6,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const score = context.parsed.y;
                                    const grade = getGradeFromScore(score);
                                    const label = context.dataset.label;
                                    const employeeData = sortedPerformers[currentPerformerOffset + context.dataIndex];
                                    const count = employeeData ? employeeData.appraisalCount : 1;
                                    return `Average ${label}: ${score}% (${grade}) - ${count} appraisal${count > 1 ? 's' : ''}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }





        // 3. Performance Grade Chart - Exact copy from admin dashboard
        function createPerformanceGradeChart(appraisals) {
            const ctx = document.getElementById('performanceGradeChart').getContext('2d');

            const gradeDistribution = {
                'Poor': 0,
                'Need Improvement': 0,
                'Meet Requirements': 0,
                'Very Good': 0,
                'Excellent': 0
            };

            // Count grades from individual appraisals (same as admin dashboard)
            appraisals.forEach(appraisal => {
                let grade = appraisal.grade;
                // Consolidate legacy grade names
                if (grade === 'Meets Requirements') {
                    grade = 'Meet Requirements';
                }
                if (grade && gradeDistribution.hasOwnProperty(grade)) {
                    gradeDistribution[grade]++;
                }
            });

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(gradeDistribution),
                    datasets: [{
                        data: Object.values(gradeDistribution),
                        backgroundColor: [
                            '#dc3545',  // Poor - Red
                            '#fd7e14',  // Need Improvement - Orange
                            '#28a745',  // Meet Requirements - Green
                            '#007bff',  // Very Good - Blue
                            '#ffc107'   // Excellent - Yellow
                        ],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 10,
                                font: { size: 10 }
                            }
                        }
                    },
                    layout: {
                        padding: { left: 5, right: 5, top: 5, bottom: 5 }
                    }
                }
            });
        }

        // 4. Performance Over Period Chart - Based on employee averages per period
        function createPerformanceOverPeriodChart(appraisals) {
            const ctx = document.getElementById('performanceOverPeriodChart').getContext('2d');

            // Group by period, then calculate employee averages within each period
            const periodEmployeeData = {};

            appraisals.forEach(appraisal => {
                const period = appraisal.period?.name || 'Unknown';
                const empCode = appraisal.employee_code;

                if (!periodEmployeeData[period]) {
                    periodEmployeeData[period] = {};
                }
                if (!periodEmployeeData[period][empCode]) {
                    periodEmployeeData[period][empCode] = {
                        scores: [],
                        employee: appraisal.employee
                    };
                }
                if (appraisal.total_score !== null && appraisal.total_score !== undefined) {
                    periodEmployeeData[period][empCode].scores.push(appraisal.total_score);
                }
            });

            // Sort periods in chronological order (Q1, Q2, Semester 1, Q3, Q4, Semester 2, Annual)
            const periodOrder = ['Q1', 'Q2', 'Semester 1', 'Q3', 'Q4', 'Semester 2', 'Annual'];
            const periods = Object.keys(periodEmployeeData).sort((a, b) => {
                const indexA = periodOrder.findIndex(p => a.includes(p));
                const indexB = periodOrder.findIndex(p => b.includes(p));
                if (indexA === -1 && indexB === -1) return a.localeCompare(b);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            });
            const averages = periods.map(period => {
                const employees = Object.values(periodEmployeeData[period]);
                const employeeAverages = employees
                    .filter(emp => emp.scores.length > 0)
                    .map(emp => emp.scores.reduce((sum, score) => sum + score, 0) / emp.scores.length);

                return employeeAverages.length > 0 ?
                    employeeAverages.reduce((sum, avg) => sum + avg, 0) / employeeAverages.length : 0;
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: periods,
                    datasets: [{
                        label: 'Average Performance %',
                        data: averages,
                        borderColor: '#e67e22',
                        backgroundColor: 'rgba(230, 126, 34, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#e67e22',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            grid: { display: false },
                            ticks: { font: { size: 10 } }
                        }
                    },
                    layout: {
                        padding: { left: 5, right: 5, top: 5, bottom: 5 }
                    }
                }
            });
        }

        // 5. Performance Bell Curve Chart - Exact copy from admin dashboard
        function createPerformanceBellCurve(appraisals) {
            // Count employees by actual grade (not score ranges)
            const gradeCount = {
                'Poor': 0,
                'Need Improvement': 0,
                'Meet Requirements': 0,
                'Very Good': 0,
                'Excellent': 0
            };

            appraisals.forEach(appraisal => {
                let grade = appraisal.grade;
                // Consolidate legacy grade names
                if (grade === 'Meets Requirements') {
                    grade = 'Meet Requirements';
                }
                if (grade && gradeCount.hasOwnProperty(grade)) {
                    gradeCount[grade]++;
                }
            });

            const labels = Object.keys(gradeCount); // X-axis: Grades
            const data = Object.values(gradeCount); // Y-axis: Number of employees
            const colors = labels.map(grade => {
                switch(grade.toLowerCase()) {
                    case 'poor': return '#dc3545'; // Red
                    case 'need improvement': return '#fd7e14'; // Orange
                    case 'meet requirements': return '#198754'; // Green
                    case 'very good': return '#0d6efd'; // Blue
                    case 'excellent': return '#ffc107'; // Yellow
                    default: return '#6c757d'; // Gray
                }
            });

            const bellCurveCanvas = document.getElementById('performanceBellCurve');
            if (bellCurveCanvas) {
                window.bellCurveChart = new Chart(bellCurveCanvas, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Number of Employees',
                            data: data,
                            borderColor: '#0d6efd',
                            backgroundColor: 'rgba(13, 110, 253, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: colors,
                            pointBorderColor: colors,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Performance Bell Curve (Number of Employees vs Grades)',
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Number of Employees'
                                },
                                ticks: {
                                    stepSize: 1,
                                    callback: function(value) {
                                        return Number.isInteger(value) ? value : '';
                                    }
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Performance Grades'
                                }
                            }
                        }
                    }
                });
            }
        }

        // 6. Top KPIs Charts - Exact copy from admin dashboard
        async function createTopKPIsCharts(appraisals) {
            try {
                console.log('Creating Top KPIs charts for', appraisals.length, 'appraisals');

                // Get KPI scores from appraisal details
                const kpiScores = {
                    performance: {},
                    behavioral: {}
                };

                // Fetch appraisal scores to get KPI scores (using denormalized structure)
                for (const appraisal of appraisals) {
                    const { data: details, error } = await supabaseClient
                        .from('appraisal_scores')
                        .select(`
                            score,
                            kpi_name,
                            kpi_category_name
                        `)
                        .eq('appraisal_id', appraisal.id);

                    if (error) {
                        console.error('Error fetching appraisal details for', appraisal.id, ':', error);
                        continue;
                    }

                    if (details && details.length > 0) {
                        console.log('Found', details.length, 'KPI scores for appraisal', appraisal.id);
                        details.forEach(detail => {
                            if (detail.kpi_name && detail.score && detail.kpi_category_name) {
                                const category = detail.kpi_category_name.toLowerCase();
                                const kpiName = detail.kpi_name;

                                if (category === 'performance') {
                                    if (!kpiScores.performance[kpiName]) {
                                        kpiScores.performance[kpiName] = { total: 0, count: 0 };
                                    }
                                    kpiScores.performance[kpiName].total += detail.score;
                                    kpiScores.performance[kpiName].count++;
                                } else if (category === 'behavioral') {
                                    if (!kpiScores.behavioral[kpiName]) {
                                        kpiScores.behavioral[kpiName] = { total: 0, count: 0 };
                                    }
                                    kpiScores.behavioral[kpiName].total += detail.score;
                                    kpiScores.behavioral[kpiName].count++;
                                }
                            }
                        });
                    } else {
                        console.log('No KPI details found for appraisal', appraisal.id);
                    }
                }

                console.log('Performance KPIs found:', Object.keys(kpiScores.performance));
                console.log('Behavioral KPIs found:', Object.keys(kpiScores.behavioral));

                // Calculate averages and get top 5
                const performanceAvgs = Object.entries(kpiScores.performance)
                    .map(([name, data]) => ({ name, avg: data.total / data.count }))
                    .sort((a, b) => b.avg - a.avg)
                    .slice(0, 5);

                const behavioralAvgs = Object.entries(kpiScores.behavioral)
                    .map(([name, data]) => ({ name, avg: data.total / data.count }))
                    .sort((a, b) => b.avg - a.avg)
                    .slice(0, 5);

                console.log('Top Performance KPIs:', performanceAvgs);
                console.log('Top Behavioral KPIs:', behavioralAvgs);

                // If no data found, show placeholder message
                if (performanceAvgs.length === 0 && behavioralAvgs.length === 0) {
                    console.log('No KPI data found - charts will be empty');
                }

                // Create Top 5 Performance KPIs chart with modern styling
                const performanceKPICanvas = document.getElementById('topPerformanceKPIsChart');
                if (performanceKPICanvas) {
                    window.topPerformanceKPIsChart = new Chart(performanceKPICanvas, {
                        type: 'bar',
                        data: {
                            labels: performanceAvgs.map(item => item.name),
                            datasets: [{
                                label: 'Score (0-5)',
                                data: performanceAvgs.map(item => item.avg),
                                backgroundColor: '#f39c12',
                                borderColor: '#f39c12',
                                borderWidth: 1,
                                borderRadius: 8,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Top 5 Performance KPIs Scores',
                                    font: { size: 14, weight: 'bold' }
                                },
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `Score: ${context.raw.toFixed(2)}/5`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 5,
                                    title: {
                                        display: true,
                                        text: 'Score (0-5)'
                                    },
                                    ticks: {
                                        stepSize: 0.5,
                                        callback: function(value) {
                                            return value.toFixed(1);
                                        }
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Performance KPIs'
                                    }
                                }
                            }
                        }
                    });
                }

            } catch (error) {
                console.error('Error creating top KPIs charts:', error);
            }
        }

        // Helper function to get average scores per employee per year
        function getAverageScoresPerEmployee(appraisals) {
            const employeeMap = {};

            // Group appraisals by employee (only for the filtered year/period)
            appraisals.forEach(appraisal => {
                const empCode = appraisal.employee_code;
                if (!employeeMap[empCode]) {
                    employeeMap[empCode] = {
                        employee: appraisal.employee,
                        employee_code: empCode,
                        totalScores: [],
                        performanceScores: [],
                        behavioralScores: [],
                        periods: []
                    };
                }

                if (appraisal.total_score !== null && appraisal.total_score !== undefined) {
                    employeeMap[empCode].totalScores.push(appraisal.total_score);
                }
                if (appraisal.performance_score !== null && appraisal.performance_score !== undefined) {
                    employeeMap[empCode].performanceScores.push(appraisal.performance_score);
                }
                if (appraisal.behavioral_score !== null && appraisal.behavioral_score !== undefined) {
                    employeeMap[empCode].behavioralScores.push(appraisal.behavioral_score);
                }
                if (appraisal.period?.name) {
                    employeeMap[empCode].periods.push(appraisal.period.name);
                }
            });

            // Calculate averages for each employee (within the selected year/period filters)
            return Object.values(employeeMap).map(emp => ({
                employee: emp.employee,
                employee_code: emp.employee_code,
                avgTotalScore: emp.totalScores.length > 0 ?
                    emp.totalScores.reduce((sum, score) => sum + score, 0) / emp.totalScores.length : 0,
                avgPerformanceScore: emp.performanceScores.length > 0 ?
                    emp.performanceScores.reduce((sum, score) => sum + score, 0) / emp.performanceScores.length : 0,
                avgBehavioralScore: emp.behavioralScores.length > 0 ?
                    emp.behavioralScores.reduce((sum, score) => sum + score, 0) / emp.behavioralScores.length : 0,
                appraisalCount: emp.totalScores.length,
                periods: [...new Set(emp.periods)] // Unique periods for this employee
            }));
        }

        // Helper function to get latest appraisal per employee (kept for other uses)
        function getLatestAppraisalPerEmployee(appraisals) {
            const employeeMap = {};
            appraisals.forEach(appraisal => {
                const empCode = appraisal.employee_code;
                if (!employeeMap[empCode] || new Date(appraisal.created_at) > new Date(employeeMap[empCode].created_at)) {
                    employeeMap[empCode] = appraisal;
                }
            });
            return Object.values(employeeMap);
        }

        // Update table - Enhanced with manager field and sorting
        function updateTable(appraisals) {
            const tbody = document.getElementById('reportTableBody');
            tbody.innerHTML = '';

            appraisals.forEach(appraisal => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${appraisal.employee?.name || 'N/A'}</td>
                    <td>${appraisal.employee?.department || 'N/A'}</td>
                    <td>${appraisal.manager?.name || 'N/A'}</td>
                    <td>${appraisal.period?.name || 'N/A'}</td>
                    <td>${appraisal.total_score || 0}%</td>
                    <td>${appraisal.performance_score || 0}%</td>
                    <td>${appraisal.behavioral_score || 0}%</td>
                    <td><span class="badge ${getGradeBadgeClass(appraisal.grade)}">${appraisal.grade || 'N/A'}</span></td>
                    <td>${appraisal.created_at ? appUtils.formatReadableDate(appraisal.created_at) : 'N/A'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewAppraisal('${appraisal.id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Sorting functionality
        let sortDirection = 'desc'; // Start with highest to lowest

        function sortTable(columnIndex) {
            // Toggle sort direction
            sortDirection = sortDirection === 'desc' ? 'asc' : 'desc';

            // Update sort icon
            const sortIcon = document.getElementById('sortIcon4');
            sortIcon.className = sortDirection === 'desc' ? 'fas fa-sort-down' : 'fas fa-sort-up';

            // Sort the filtered appraisals
            filteredAppraisals.sort((a, b) => {
                const aValue = parseFloat(a.total_score) || 0;
                const bValue = parseFloat(b.total_score) || 0;

                if (sortDirection === 'desc') {
                    return bValue - aValue; // Highest to lowest
                } else {
                    return aValue - bValue; // Lowest to highest
                }
            });

            // Update table
            updateTable(filteredAppraisals);
        }

        // View appraisal function
        function viewAppraisal(appraisalId) {
            window.open(`view-appraisal.html?id=${appraisalId}`, '_blank');
        }

        // Get badge class for grade - Using unified color system
        function getGradeBadgeClass(grade) {
            switch (grade) {
                case 'Poor': return 'badge text-white' + '" style="background-color: #dc3545'; // Red
                case 'Need Improvement': return 'badge text-white' + '" style="background-color: #fd7e14'; // Orange
                case 'Meet Requirements':
                case 'Meets Requirements': return 'badge text-white' + '" style="background-color: #198754'; // Green
                case 'Very Good': return 'badge text-white' + '" style="background-color: #0d6efd'; // Blue
                case 'Excellent': return 'badge text-dark' + '" style="background-color: #ffc107'; // Yellow
                default: return 'badge-secondary';
            }
        }

        // Export report
        function exportReport() {
            const wb = XLSX.utils.book_new();

            // Summary data
            const summaryData = [
                ['Performance Report Summary'],
                [''],
                ['Total Appraisals', document.getElementById('totalAppraisals').textContent],
                ['Average Score', document.getElementById('averageScore').textContent],
                ['Top Performers', document.getElementById('topPerformers').textContent],
                ['Need Improvement', document.getElementById('improvementNeeded').textContent],
                [''],
                ['Generated on', new Date().toLocaleDateString()],
                ['Generated by', currentManager.name]
            ];

            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

            // Detailed data
            const detailedData = [
                ['Employee', 'Department', 'Position', 'Period', 'Total Score', 'Performance Score', 'Behavioral Score', 'Grade', 'Date']
            ];

            filteredAppraisals.forEach(appraisal => {
                detailedData.push([
                    appraisal.employee?.name || 'N/A',
                    appraisal.employee?.department || 'N/A',
                    appraisal.employee?.position || 'N/A',
                    appraisal.period?.name || 'N/A',
                    `${appraisal.total_score || 0}%`,
                    `${appraisal.performance_score || 0}%`,
                    `${appraisal.behavioral_score || 0}%`,
                    appraisal.grade || 'N/A',
                    appraisal.created_at ? appUtils.formatReadableDate(appraisal.created_at) : 'N/A'
                ]);
            });

            const detailedWs = XLSX.utils.aoa_to_sheet(detailedData);
            XLSX.utils.book_append_sheet(wb, detailedWs, 'Detailed Report');

            // Save file
            const fileName = `Performance_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // Export table only
        function exportTable() {
            const table = document.getElementById('reportTable');
            const wb = XLSX.utils.table_to_book(table);
            const fileName = `Performance_Table_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // Print report
        function printReport() {
            window.print();
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });

        // Chart toggle functionality
        let chartsVisible = true;

        function toggleCharts() {
            const chartsSection = document.getElementById('chartsSection');
            const toggleBtn = document.getElementById('toggleChartsBtn');
            const toggleText = document.getElementById('toggleChartsText');
            const toggleIcon = toggleBtn.querySelector('i');

            if (chartsVisible) {
                // Hide charts with smooth animation
                chartsSection.style.transition = 'all 0.3s ease-in-out';
                chartsSection.style.opacity = '0';
                chartsSection.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    chartsSection.style.display = 'none';
                    toggleText.textContent = 'Show Charts';
                    toggleIcon.className = 'fas fa-chart-line me-1';
                    toggleBtn.className = 'btn btn-success btn-sm';
                    toggleBtn.title = 'Show Charts';
                }, 300);

                chartsVisible = false;
            } else {
                // Show charts with smooth animation
                chartsSection.style.display = 'block';
                chartsSection.style.transition = 'all 0.3s ease-in-out';

                setTimeout(() => {
                    chartsSection.style.opacity = '1';
                    chartsSection.style.transform = 'translateY(0)';
                    toggleText.textContent = 'Hide Charts';
                    toggleIcon.className = 'fas fa-chart-bar me-1';
                    toggleBtn.className = 'btn btn-outline-primary btn-sm';
                    toggleBtn.title = 'Hide Charts';
                }, 10);

                chartsVisible = true;
            }
        }

        // Add smooth hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.getElementById('toggleChartsBtn');

            if (toggleBtn) {
                toggleBtn.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.2s ease';
                });

                toggleBtn.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            }
        });

    </script>
